name: app_giftmachine
description: "冰蝶设备-礼品机"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.3.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.6

  flutter_screenutil: ^5.9.3 # 屏幕适配
  get: ^4.6.6 # 状态管理、路由管理、依赖注入
  carousel_slider: ^4.2.1 # 轮播图
  lottie: ^3.1.0 # lottie动画运行库
  package_info_plus: ^5.0.1 # 查询包信息
  device_info_plus: ^9.1.2
  restart_app: ^1.2.1
  modal_bottom_sheet: ^3.0.0 # 底部弹出窗

  package_interface:
    path: ../foundation/package_interface
  plugin_serial:
    path: ../foundation/plugin_serial
  package_cardreader:
    path: ../foundation/package_cardreader
  package_common:
    path: ../foundation/package_common
  package_ui:
    path: ../foundation/package_ui
  package_i18n:
    path: ../foundation/package_i18n
  package_business:
    path: ../foundation/package_business

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "ic_launcher"
  ios: false
  image_path: "assets/icon/icon_logo.png"
  min_sdk_android: 21

flutter:

  uses-material-design: true

  assets:
    - assets/i18n/
    - assets/icon/
    - assets/lottie/
