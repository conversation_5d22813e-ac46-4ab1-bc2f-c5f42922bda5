import 'package:flutter/material.dart';

class DeviceStateWidget extends StatefulWidget {
  final Icon icon;
  final bool connected;
  final double size;

  const DeviceStateWidget(
      {required this.icon, required this.connected, required this.size});

  @override
  State<StatefulWidget> createState() => DeviceStateWidgetState();
}

class DeviceStateWidgetState extends State<DeviceStateWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(5),
          child: widget.icon,
        ),
        Positioned(
            top: 0,
            right: 0,
            child: Container(
              height: widget.size,
              width: widget.size,
              decoration: BoxDecoration(
                  color: widget.connected ? Colors.green : Colors.red,
                  borderRadius:
                      BorderRadius.all(Radius.circular(widget.size))),
              child: Icon(
                widget.connected ? Icons.check : Icons.close,
                size: widget.size - 5,
                color: Colors.white,
              ),
            ))
      ],
    );
  }
}
