import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:package_cardreader/CardReaderFinder.dart';
import 'package:package_cardreader/ICardReaderManager.dart';
import 'package:package_common/package_common.dart';
import 'package:plugin_serial/serial/MessageType.dart';

class BootstrapDevice extends BootstrapBase {
  @override
  int get delaySeconds => 5;

  @override
  Future<void> execute() async {
    initCardReader();
  }

  Future<void> initCardReader() async {
    try {
      ICardReaderManager? manager = await CardReaderFinder.createInstance(
          portName: Env.appContext.cardReaderSerialPort);
      if (manager == null) return;

      // 设置监听器
      Env.cardReaderManager = manager;
      Env.cardReaderManager!.onConnect(null);
      Env.cardReaderManager!.onReadCard((cards) {
        Env.eventManager.publish(EventReadCard(cards));
      });
      Env.cardReaderManager!
          .onMessage((MessageType type, String tag, String msg) {
        if (type == MessageType.error) {
          Env.logger.error(tag, msg);
        } else {
          Env.logger.info(tag, msg);
        }
      });

      await Env.cacheManager.set<String>(
          ConfigName.config_cardReaderSerialPort, manager.getPortName);
      Env.appContext.cardReaderSerialPort = manager.getPortName;

      Env.eventManager.publish(EventCardReaderConnected());
    } catch (e) {
      print(e);
    }
  }
}
