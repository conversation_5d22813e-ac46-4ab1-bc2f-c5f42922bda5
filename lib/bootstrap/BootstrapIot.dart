import 'package:get/get.dart';
import 'package:package_business/package_business.dart';
import 'package:package_common/package_common.dart';
import 'package:package_ui/page/RestartWidget.dart';

import '../global/ConfigName.dart';
import '../global/Env.dart';

class BootstrapIot extends BootstrapBase {
  final String eventKey_networkStateChange = 'eventKey_iot_networkStateChange';

  @override
  int get delaySeconds => 30;

  @override
  Future<void> execute() async {
    // 从飞翎服务器获取Iot设备信息
    if (Env.appContext.iotSecret.isEmpty && Env.appContext.portNO > 0) {
      String iotName = 'ticketmachine-${Env.appContext.portNO}';
      final res = await MacService(Env.httpManager).getIotInfo(iotName);

      // 保存到本地缓存
      await Env.cacheManager
          .set<String>(ConfigName.config_iotName, res.iotDeviceName ?? '');
      await Env.cacheManager
          .get<String>(ConfigName.config_iotSecret, res.iotDeviceSecret ?? '');

      // 更新全局参数
      Env.appContext.iotName = res.iotDeviceName ?? '';
      Env.appContext.iotSecret = res.iotDeviceSecret ?? '';
    }

    await iotConnect();

    // 若网络状态变更则尝试重连
    Env.eventManager.subscribe<EventNetworkStateChange>(
        eventKey_networkStateChange, (eventObject) async {
      // 如果网络断开重连，需要重新连接
      if (eventObject.connected) {
        await iotConnect();
      }
    });
  }

  iotConnect() async {
    // 如果iot信息齐全则连接iot服务器
    if (Env.appContext.iotName.isNotEmpty &&
        Env.appContext.iotSecret.isNotEmpty) {
      final deviceInfo = await buildDeviceInfo();

      Env.iotManager.stop();

      Env.iotManager
          .setListener(iotListener)
          .setDeviceInfo(deviceInfo)
          .setCacheManager(Env.cacheManager)
          .setUpgradeManager(Env.upgradeManager)
          .setRestartCallback(restartCallback)
          .start(Env.appContext.iotName, Env.appContext.iotSecret);
    }
  }

  void iotListener(String topic, String message) {
    Env.logger.info('iot', '收到消息，主题：$topic，消息：$message');
    print('[IOT]收到消息，主题：$topic，消息：$message');
  }

  /// 构建设备信息
  Future<IotDeviceInfo> buildDeviceInfo() async {
    String deviceName = 'ticketmachine-${Env.appContext.portNO}';
    String storeId = Env.appContext.store == null
        ? '--'
        : (Env.appContext.store!.id ?? '--');

    String site = '';
    final position = await Env.locationManager.getLocation();
    if (position != null) {
      site = '${position.city} ${position.regionName} ${position.country}';
    }

    return IotDeviceInfo(
        deviceName: deviceName,
        deviceType: 4,
        location: site,
        storeId: storeId,
        portNO: Env.appContext.portNO,
        version: Env.appContext.version,
        packageName: Env.appContext.packageName,
        offlineVersionCode: Env.appContext.offlineVersionCode);
  }

  void restartCallback() {
    RestartWidget.restartApp(Get.context!);
  }
}
