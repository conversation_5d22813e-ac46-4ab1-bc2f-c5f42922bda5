import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:package_common/bootstrap/BootstrapBase.dart';

class BootstrapEnv extends BootstrapBase {
  @override
  Future<void> execute() async {
    // 配置日志管理器
    Env.logger.setEventManager(Env.eventManager);

    // 配置网络管理器
    Env.httpManager
        .setBaseUrl(Env.appContext.baseUrl)
        .setLogger(Env.logger)
        .setToken(Env.appContext.token);

    // 配置升级管理器
    Env.upgradeManager
        .setCheckUrl(
            'https://bingdiekeji-erp-common.oss-cn-qingdao.aliyuncs.com/bingdie-device/giftmachine/update.json')
        .setListener(upgradeListener);

    // 调试信息
    Env.debugManager.testCard = Env.appContext.testCard;
    Env.debugManager.testQRCode = Env.appContext.testQRCode;
    Env.debugManager.simulateCardChange = (card) async {
      await Env.cacheManager.set<String>(ConfigName.config_testCard, card);
      Env.appContext.testCard = card;
    };
    Env.debugManager.simulateQRCodeChange = (code) async {
      await Env.cacheManager.set<String>(ConfigName.config_testQRCode, code);
      Env.appContext.testQRCode = code;
    };
  }

  void upgradeListener(double process) {}
}
