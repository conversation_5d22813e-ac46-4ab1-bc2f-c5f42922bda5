import 'dart:convert';

import 'package:app_giftmachine/global/Env.dart';
import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/ThemeContext.dart';
import 'package:package_business/package_business.dart';
import 'package:package_common/bootstrap/BootstrapBase.dart';
import 'package:package_common/util/SHAUtil.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';

class BootstrapConfig extends BootstrapBase {
  @override
  Future<void> execute() async {
    await _initCommunicationParams();

    await _initIotParams();

    await _initDeviceParams();

    await _initOtherParams();
  }

  /// 初始化服务器通信配置
  _initCommunicationParams() async {
    // 通信配置
    Env.appContext.baseUrl =
        await Env.cacheManager.get<String>(ConfigName.config_baseUrl, '');

    // 门店
    String? storeJson =
        await Env.cacheManager.get<String>(ConfigName.config_store, '');
    if (storeJson.isNotEmpty) {
      Env.appContext.store = MStore.fromJson(jsonDecode(storeJson));
    }

    // 终端
    String? terminalJson =
        await Env.cacheManager.get<String>(ConfigName.config_terminal, '');
    if (terminalJson.isNotEmpty) {
      Env.appContext.terminal = MTerminal.fromJson(jsonDecode(terminalJson));
    }

    // 端口
    String? portJson =
        await Env.cacheManager.get<String>(ConfigName.config_macPort, '');
    if (portJson.isNotEmpty) {
      Env.appContext.macPort = MMacPort.fromJson(jsonDecode(portJson));
    }

    // 登录账号
    Env.appContext.loginAccount =
        await Env.cacheManager.get<String>(ConfigName.config_loginAccount, '');

    // 登录密码
    Env.appContext.loginPassword =
        await Env.cacheManager.get<String>(ConfigName.config_loginPassword, '');

    // 端口号
    int? portNO = await Env.cacheManager.get<int>(ConfigName.config_portNO, 0);
    Env.appContext.portNO = portNO;
  }

  /// 初始化物联网配置
  _initIotParams() async {
    // 物联网设备名
    Env.appContext.iotName =
        await Env.cacheManager.get<String>(ConfigName.config_iotName, '');

    // 物联网设备密钥
    Env.appContext.iotSecret =
        await Env.cacheManager.get<String>(ConfigName.config_iotSecret, '');

    // 生成设备唯一码
    var deviceInfo = await DeviceInfoPlugin().androidInfo;
    List<String> params = [
      deviceInfo.id,
      deviceInfo.serialNumber,
      deviceInfo.manufacturer,
      deviceInfo.model,
      deviceInfo.product,
      deviceInfo.fingerprint,
      deviceInfo.host,
      deviceInfo.board,
      deviceInfo.hardware,
    ];
    Env.appContext.uniqueId = SHAUtil.generateBySHA256(params);
  }

  /// 初始化设备信息
  _initDeviceParams() async {
    // 读卡板串口
    Env.appContext.cardReaderSerialPort = await Env.cacheManager
        .get<String>(ConfigName.config_cardReaderSerialPort, '');

    // 获取软件信息
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    Env.appContext.version = packageInfo.version;
    Env.appContext.packageName = packageInfo.packageName;
    Env.appContext.offlineVersionCode =
        int.tryParse(packageInfo.buildNumber) ?? 0;
  }

  /// 初始化其他配置
  _initOtherParams() async {
    // 物联网设备密钥
    Env.appContext.langCode =
        await Env.cacheManager.get<String>(ConfigName.config_language, 'zh');

    // 主题
    String themeConfig =
        await Env.cacheManager.get<String>(ConfigName.config_theme, '');
    if (themeConfig.isNotEmpty) {
      try {
        Env.themeContext = ThemeContext.fromJson(jsonDecode(themeConfig));
      } catch (e) {
        Env.themeContext = ThemeContext.defaultTheme();
      }
    } else {
      Env.themeContext = ThemeContext.defaultTheme();
    }

    // 分辨率
    String dpiConfig =
        await Env.cacheManager.get<String>(ConfigName.config_dpi, '');
    if (dpiConfig.isNotEmpty) {
      Env.appContext.dpi = (jsonDecode(dpiConfig) as List<dynamic>)
          .map((e) => e as int)
          .toList();
    }

    // 调试
    Env.appContext.testCard =
        await Env.cacheManager.get<String>(ConfigName.config_testCard, '');
    Env.appContext.testQRCode =
        await Env.cacheManager.get<String>(ConfigName.config_testQRCode, '');
  }
}
