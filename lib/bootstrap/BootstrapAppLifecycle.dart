import 'package:app_giftmachine/global/Env.dart';
import 'package:get/get.dart';
import 'package:package_common/bootstrap/BootstrapBase.dart';
import 'package:package_ui/page/AppController.dart';

class BootstrapAppLifecycle extends BootstrapBase {
  @override
  Future<void> execute() async {
    final appController = AppController();
    appController.onDetached = _onDetached;
    Get.put(appController);
  }
}

Future<void> _onDetached() async {
  await Env.dispose();
}
