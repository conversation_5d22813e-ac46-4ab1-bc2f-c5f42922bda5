import 'package:app_giftmachine/global/Env.dart';
import 'package:package_common/event/EventNetworkStateChange.dart';
import 'package:package_interface/task/ScheduleBase.dart';
import 'package:package_common/util/HttpUtil.dart';

class ScheduleCheckNetwork extends ScheduleBase {
  @override
  String get name => '检查网络';

  @override
  int get seconds => 5;

  bool connected = true;

  @override
  Future<void> execute() async {
    try {
      final current = await HttpUtil.checkNetwork();
      if (current != connected) {
        connected = current;
        Env.eventManager.publish(EventNetworkStateChange(current));
      }
    } catch (e) {}
  }
}
