import 'package:package_business/package_business.dart';

class AppContext {
  // 机台类型
  // String macCate = MacCate.giftMachine;
  // String setupCodeCate = 'giftMachine';
  String macCate = MacCate.ticketmachine;
  String setupCodeCate = 'ticketmachine';

  // 服务器通信配置
  String baseUrl = '';
  MStore? store;
  String? loginAccount;
  String? loginPassword;
  MTerminal? terminal;
  MMacPort? macPort;
  int portNO = 0;
  String token = '';

  // 物联网配置
  String iotName = '';
  String iotSecret = '';
  String uniqueId = '';

  // 升级配置
  String version = '';
  String packageName = '';
  int offlineVersionCode = 0;

  // 其他配置
  String langCode = 'zh';
  List<int>? dpi;

  // 设备配置
  String cardReaderSerialPort = '';

  // 调试配置
  String testCard = '';
  String testQRCode = '';
}
