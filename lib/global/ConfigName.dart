class ConfigName {
  // 服务器通信配置
  static String config_baseUrl = 'config_baseUrl';
  static String config_store = 'config_store';
  static String config_terminal = 'config_terminal';
  static String config_macPort = 'config_macPort';
  static String config_loginAccount = 'config_loginAccount';
  static String config_loginPassword = 'config_loginPassword';
  static String config_portNO = 'config_portNO';

  // 物联网配置
  static String config_iotName = 'config_iotName';
  static String config_iotSecret = 'config_iotSecret';

  // 其他配置
  static String config_staffCode = 'config_staffCode';
  static String config_language = 'config_language';
  static String config_theme = 'config_theme';
  static String config_dpi = 'config_dpi';

  // 读卡板配置
  static String config_cardReaderSerialPort = 'config_cardReaderSerialPort';

  // 打印配置
  static String config_tscTicketPrinterConfig = 'config_tscTicketPrinterConfig';
  static String config_ticketDebugLine1 = 'config_ticketDebugLine1';
  static String config_ticketDebugLine2 = 'config_ticketDebugLine2';
  static String config_ticketDebugQrcode = 'config_ticketDebugQrcode';

  // 调试配置
  static String config_testCard = 'config_testCard';
  static String config_testQRCode = 'config_testQRCode';
}