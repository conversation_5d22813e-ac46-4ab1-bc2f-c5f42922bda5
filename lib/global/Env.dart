import 'package:app_giftmachine/bootstrap/BootstrapAppLifecycle.dart';
import 'package:app_giftmachine/bootstrap/BootstrapIot.dart';
import 'package:app_giftmachine/bootstrap/BootstrapTask.dart';
import 'package:app_giftmachine/destroy/DestroyDevice.dart';
import 'package:app_giftmachine/destroy/DestroyIot.dart';
import 'package:app_giftmachine/global/ThemeContext.dart';
import 'package:package_interface/logger/ILogger.dart';
import 'package:package_interface/task/IScheduleManager.dart';
import 'package:package_interface/upgrade/IUpgradeManager.dart';
import 'package:package_ui/PromptManager.dart';
import 'package:package_i18n/I18nManager.dart';
import 'package:package_cardreader/ICardReaderManager.dart';
import 'package:package_common/package_common.dart';

import '../bootstrap/BootstrapConfig.dart';
import '../bootstrap/BootstrapDevice.dart';
import '../bootstrap/BootstrapEnv.dart';
import 'AppContext.dart';

class Env {
  static LocationManager locationManager = LocationManager();
  static AppContext appContext = AppContext();
  static PromptManager promptManager = PromptManager();
  static ThemeContext themeContext = ThemeContext();
  static EventManager eventManager = EventManager();
  static CacheManager cacheManager = CacheManager();
  static ILogger logger = FileLogger();
  static IotManager iotManager = IotManager();
  static I18nManager i18nManager = I18nManager();
  static IUpgradeManager upgradeManager = UpgradeManager();
  static HttpManager httpManager = HttpManager();
  static WifiManager wifiManager = WifiManager();
  static IScheduleManager scheduleManager = ScheduleManager();
  static DebugManager debugManager = DebugManager(Env.eventManager);

  // 硬件管理者
  static ICardReaderManager? cardReaderManager;

  static Future<void> bootstrap() async {
    final bootstrapManager = BootstrapManager();
    bootstrapManager.add(BootstrapAppLifecycle());
    bootstrapManager.add(BootstrapConfig());
    bootstrapManager.add(BootstrapEnv());
    bootstrapManager.add(BootstrapTask());
    bootstrapManager.add(BootstrapDevice());
    bootstrapManager.add(BootstrapIot());
    await bootstrapManager.init();
  }

  static Future<void> dispose() async {
    final destroyManager = DestroyManager();
    destroyManager.add(DestroyDevice());
    destroyManager.add(DestroyIot());
    await destroyManager.dispose();
  }
}
