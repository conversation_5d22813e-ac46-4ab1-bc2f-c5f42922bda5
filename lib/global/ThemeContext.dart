class ThemeContext {
  // 主界面显示区域主题
  String? name;
  HomeDisplayMode? homeDisplayMode;
  int? homeDisplayBeginColor;
  int? homeDisplayEndColor;
  String? homeDisplayImageUrl;
  int? homeDisplayFontColor;

  ThemeContext(
      {this.name,
      this.homeDisplayMode,
      this.homeDisplayBeginColor,
      this.homeDisplayEndColor,
      this.homeDisplayFontColor,
      this.homeDisplayImageUrl});

  ThemeContext.fromJson(Map<String, dynamic> json) {
    name = json['Name'] ?? name;
    homeDisplayMode = _convertStringToMode(json['HomeDisplayMode'] ?? 'none');
    homeDisplayBeginColor = json['HomeDisplayBeginColor'] ?? 0x00000000;
    homeDisplayEndColor = json['HomeDisplayEndColor'] ?? 0x00000000;
    homeDisplayFontColor = json['HomeDisplayFontColor'] ?? 0xFFFFFFFF;
    homeDisplayImageUrl = json['HomeDisplayImageUrl'] ?? '';
  }

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'HomeDisplayMode':
          _convertModeToString(homeDisplayMode ?? HomeDisplayMode.none),
      'HomeDisplayBeginColor': homeDisplayBeginColor,
      'HomeDisplayEndColor': homeDisplayEndColor,
      'HomeDisplayFontColor': homeDisplayFontColor,
      'HomeDisplayImageUrl': homeDisplayImageUrl,
    };
  }

  static _convertModeToString(HomeDisplayMode mode) {
    switch (mode) {
      case HomeDisplayMode.color:
        return 'color';
      case HomeDisplayMode.image:
        return 'image';
      default:
        return 'none';
    }
  }

  static _convertStringToMode(String mode) {
    switch (mode) {
      case 'color':
        return HomeDisplayMode.color;
      case 'image':
        return HomeDisplayMode.image;
      default:
        return HomeDisplayMode.none;
    }
  }

  static defaultTheme() {
    return ThemeContext(
        name: '默认',
        homeDisplayMode: HomeDisplayMode.image,
        homeDisplayImageUrl: 'assets/icon/icon_background.png',
        homeDisplayFontColor: 0xFFFFFFFF);
  }
}

enum HomeDisplayMode { none, color, image }
