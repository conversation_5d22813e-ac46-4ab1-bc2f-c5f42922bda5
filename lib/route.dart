import 'package:get/get.dart';
import 'package:app_giftmachine/page/page.dart';

class AppRoute {
  static final List<GetPage<dynamic>> pages = [
    GetPage(
      name: '/',
      page: () => const LoginPage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<LoginController>(() => LoginController())),
    ),
    GetPage(
      name: '/home',
      page: () => const HomePage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<HomeController>(() => HomeController())),
    ),
    GetPage(
      name: '/gift',
      page: () => const GiftPage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<GiftController>(() => GiftController())),
    ),
    GetPage(
      name: '/setup',
      page: () => const SettingByQRCodePage(),
      binding: BindingsBuilder(() => Get.lazyPut<SettingByQRCodeController>(
          () => SettingByQRCodeController())),
    ),
    GetPage(name: '/manage', page: () => const ManagePage(), bindings: [
      BindingsBuilder(
          () => Get.lazyPut<ManageController>(() => ManageController())),
      BindingsBuilder(() => Get.lazyPut<CommunicationController>(
          () => CommunicationController())),
      BindingsBuilder(
          () => Get.lazyPut<OtherController>(() => OtherController())),
    ]),
    GetPage(
      name: '/error',
      page: () => const ErrorPage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<ErrorController>(() => ErrorController())),
    ),
    GetPage(
      name: '/success',
      page: () => const SuccessPage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<SuccessController>(() => SuccessController())),
    ),
    GetPage(
      name: '/giftquery',
      page: () => const GiftQueryPage(),
      binding: BindingsBuilder(
          () => Get.lazyPut<GiftQueryController>(() => GiftQueryController())),
    ),
  ];
}
