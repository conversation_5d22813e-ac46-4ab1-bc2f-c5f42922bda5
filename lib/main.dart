import 'dart:async';

import 'package:app_giftmachine/global/Env.dart';
import 'package:app_giftmachine/route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_ui/lifecycle/RouteAwareWidget.dart';
import 'package:package_ui/page/RestartWidget.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化环境
  await Env.bootstrap();

  runApp(RestartWidget(
    child: ScreenUtilInit(
        designSize: const Size(600, 800),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GetMaterialApp(
            navigatorObservers: [RouteAwareWidget.routeObserver],
            initialRoute: '/',
            localizationsDelegates:
                Env.i18nManager.init(langCode: Env.appContext.langCode),
            getPages: AppRoute.pages,
            builder: EasyLoading.init(),
          );
        }),
  ));
}