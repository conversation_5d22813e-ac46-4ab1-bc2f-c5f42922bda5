import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_ui/page/BaseStatePage.dart';
import 'package:package_ui/page/ScanCodeWidget.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../component/DeviceStateWidget.dart';
import 'HomeController.dart';

class HomePage extends BaseStatePage<HomeController> {
  const HomePage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Stack(
        children: [
          // 主要内容
          GestureDetector(
            onTap: () => controller.resetIdleTimer(),
            onPanDown: (_) => controller.resetIdleTimer(),
            child: _buildMainContent(context),
          ),
          // 全屏广告覆盖层
          Obx(() {
            if (!controller.showFullScreenAd.value) {
              return const SizedBox.shrink();
            }
            return _buildFullScreenAd();
          }),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          '智能礼品机',
          style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // 时间显示
          Padding(
            padding: EdgeInsets.only(right: 20.w),
            child: Center(
              child: Obx(() {
                return Text(
                  controller.time.value,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                  ),
                );
              }),
            ),
          ),
        ],
      ),
      body: ScanCodeWidget(
        onScan: (code) async {
          await controller.onScanCode(code);
        },
        child: Column(
          children: [
            // 会员登录区域
            _buildScanPrompt(),
            // 提示信息
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.card_giftcard,
                      size: 80.w,
                      color: Colors.grey[300],
                    ),
                    SizedBox(height: 20.h),
                    GestureDetector(
                      onTap: () async {
                        controller.resetIdleTimer();
                        await controller.onReadCard('QQ0DABW+Px5gWsX4hODXcg==');
                      },
                      child: Text(
                        '请先刷卡或扫码登录',
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: Colors.grey[500],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      '登录后即可查看和兑换精美礼品',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      // 底部状态栏
      bottomNavigationBar: _buildButton(context),
    );
  }

  /// 构建扫描提示界面
  Widget _buildScanPrompt() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.person_outline,
            size: 60.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            '请刷卡或扫码获取会员信息',
            style: TextStyle(
              fontSize: 20.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 24.h),
          Row(
            children: [
              Expanded(
                child: _buildScanButton(
                  icon: Icons.credit_card,
                  text: '刷会员卡',
                  color: Colors.blue,
                  onTap: () async {},
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildScanButton(
                  icon: Icons.qr_code_scanner,
                  text: '扫二维码',
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建扫描按钮
  Widget _buildScanButton({
    required IconData icon,
    required String text,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: () {
        controller.resetIdleTimer();
        if (onTap != null) onTap();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32.w,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              text,
              style: TextStyle(
                fontSize: 16.sp,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context) {
    return Container(
      height: 70.h,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 版本信息
          GestureDetector(
            onTap: () {
              controller.showPasswordKeyboard(context);
            },
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20.w,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 6.w),
                Text(
                  'v${Env.appContext.version}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          // 设备状态
          Row(
            children: [
              Obx(() {
                return DeviceStateWidget(
                  icon: Icon(
                    Icons.cast_connected_rounded,
                    size: 20.w,
                  ),
                  connected: controller.cardReaderConnected.value,
                  size: 16.w,
                );
              }),
              SizedBox(width: 16.w),
              // 设备编号
              Text(
                'NO.${Env.appContext.portNO}',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建全屏广告
  Widget _buildFullScreenAd() {
    return GestureDetector(
      onTap: () {
        controller.closeFullScreenAd();
      },
      child: Container(
        color: Colors.black,
        width: double.infinity,
        height: double.infinity,
        child: Obx(() {
          if (controller.adImages.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          }

          if (controller.adImages.length == 1) {
            // 单张图片直接显示
            return Stack(
              children: [
                Center(
                  child: Image.network(
                    controller.adImages[0],
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Icon(
                          Icons.error,
                          color: Colors.white,
                          size: 50,
                        ),
                      );
                    },
                  ),
                ),
                _buildAdCloseButton(),
              ],
            );
          }

          // 多张图片轮播
          return Stack(
            children: [
              CarouselSlider(
                options: CarouselOptions(
                  height: double.infinity,
                  viewportFraction: 1.0,
                  autoPlay: true,
                  autoPlayInterval: const Duration(seconds: 5),
                  enableInfiniteScroll: true,
                  enlargeCenterPage: false,
                ),
                items: controller.adImages.map((imageUrl) {
                  return Builder(
                    builder: (BuildContext context) {
                      return Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return const Center(
                            child: CircularProgressIndicator(color: Colors.white),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.error,
                              color: Colors.white,
                              size: 50,
                            ),
                          );
                        },
                      );
                    },
                  );
                }).toList(),
              ),
              _buildAdCloseButton(),
            ],
          );
        }),
      ),
    );
  }

  /// 构建广告关闭按钮
  Widget _buildAdCloseButton() {
    return Positioned(
      top: 50.h,
      right: 30.w,
      child: GestureDetector(
        onTap: () {
          controller.closeFullScreenAd();
        },
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.close,
            color: Colors.white,
            size: 24.w,
          ),
        ),
      ),
    );
  }
}
