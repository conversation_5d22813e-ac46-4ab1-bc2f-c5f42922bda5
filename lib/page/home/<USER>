import 'dart:async';

import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_business/package_business.dart';
import 'package:package_common/event/EventCardReaderConnected.dart';
import 'package:package_common/event/EventReadCard.dart';
import 'package:package_common/util/StringUtil.dart';
import 'package:package_ui/page/BaseController.dart';

class HomeController extends BaseController {
  final String eventKeyCardreaderConnected =
      'eventKey_home_cardreader_connected';
  final String eventKey_readCard = 'eventKey_home_readCard';

  Rx<bool> cardReaderConnected = Rx(false);
  Timer? _timer;
  Rx<String> date = Rx('--/--/--');
  Rx<String> time = Rx('--:--');

  // 会员相关状态
  final Rx<MMember?> currentMember = Rx<MMember?>(null);

  // 广告相关状态
  final RxBool showFullScreenAd = false.obs;
  Timer? _idleTimer;
  final RxList<String> adImages = <String>[].obs;

  // 示例广告图片URL列表
  final List<String> _adUrls = [
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?w=800&h=600&fit=crop',
  ];

  // 空闲超时时间（秒）
  final int _idleTimeoutSeconds = 10;

  @override
  String get uniqueId => 'HomeController';

  @override
  onPageActive() async {
    super.onPageActive();

    _close();

    cardReaderConnected.value = Env.cardReaderManager != null;

    // 监听读卡器连接事件
    Env.eventManager.subscribe<EventCardReaderConnected>(
        eventKeyCardreaderConnected, (eventObject) async {
      cardReaderConnected.value = true;
    });

    // 监听读卡事件
    Env.eventManager.subscribe<EventReadCard>(eventKey_readCard,
        (eventObject) async {
      _getMem(icCard: eventObject.icCard);
    });

    startTimer();
    
    // 初始化广告图片列表
    _initAdImages();
    
    // 开始空闲检测
    _startIdleTimer();
  }

  @override
  onPageInactive() {
    _close();
    super.onPageInactive();
  }

  void startTimer() {
    time.value = StringUtil.getHm();
    date.value = StringUtil.getDate();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      time.value = StringUtil.getHm();
    });
  }

  _close() {
    Env.eventManager.unsubscribe(eventKeyCardreaderConnected);

    if (_timer != null) {
      _timer!.cancel();
    }
    
    _idleTimer?.cancel();
  }

  showPasswordKeyboard(BuildContext context) {
    Env.promptManager.showNumberKeyboard(context,
        width: MediaQuery.of(context).size.width - 100.w,
        displayStyle: TextStyle(fontSize: 30.sp),
        numberStyle: TextStyle(fontSize: 30.sp),
        confirmStyle: TextStyle(fontSize: 30.sp),
        backStyle: TextStyle(fontSize: 30.sp, color: Colors.white),
        backButtonHeight: 60.w,
        deleteSize: 40.w, onTap: (val) {
      Get.toNamed('/manage');
    });
  }

  onScanCode(String qrcode) async {
    await _getMem(qrcode: qrcode);
  }

  onReadCard(String icCard) async {
    await _getMem(icCard: icCard);
  }

  _getMem({String? qrcode, String? icCard}) async {
    try {
      Env.promptManager.showLoading();

      MMember? member;
      if (qrcode != null && qrcode.isNotEmpty) {
        member = await MemService(Env.httpManager).getByQrCode(qrcode);
      } else if (icCard != null && icCard.isNotEmpty) {
        member = await MemService(Env.httpManager).getByICCard(icCard);
      }

      if (member == null) {
        throw Exception('未找到会员');
      }

      Get.toNamed('/gift', arguments: {'member': member});
    } catch (e) {
      Env.promptManager.toastError(e.toString());
    } finally {
      Env.promptManager.closeLoading();
    }
  }

  /// 初始化广告图片
  void _initAdImages() {
    adImages.value = _adUrls;
  }
  
  /// 开始空闲计时器
  void _startIdleTimer() {
    _idleTimer?.cancel();
    _idleTimer = Timer(Duration(seconds: _idleTimeoutSeconds), () {
      _showFullScreenAd();
    });
  }
  
  /// 重置空闲计时器
  void resetIdleTimer() {
    if (showFullScreenAd.value) {
      closeFullScreenAd();
    } else {
      _startIdleTimer();
    }
  }
  
  /// 显示全屏广告
  void _showFullScreenAd() {
    if (adImages.isNotEmpty) {
      showFullScreenAd.value = true;
    }
  }
  
  /// 关闭全屏广告
  void closeFullScreenAd() {
    showFullScreenAd.value = false;
    _startIdleTimer();
  }
}
