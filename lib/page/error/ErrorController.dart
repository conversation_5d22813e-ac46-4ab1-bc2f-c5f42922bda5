import 'package:app_giftmachine/global/Env.dart';
import 'package:get/get.dart';
import 'package:package_common/event/EventNetworkStateChange.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/BaseController.dart';

class ErrorController extends BaseController {
  final String eventKey_networkChange = 'eventKey_error_networkChange';

  Rx<String> errMsg = Rx('');

  @override
  String get uniqueId => 'ErrorController';

  @override
  void onPageActive() {
    super.onPageActive();

    if (Get.arguments != null && Get.arguments['errMsg'] != null) {
      errMsg.value = Get.arguments['errMsg'];
    } else {
      errMsg.value = '未知错误'.$t;
    }

    // 订阅网络状态变更
    Env.eventManager.subscribe<EventNetworkStateChange>(eventKey_networkChange,
        (eventObject) async {
      // 如果已经处理错误页面，则不再触发
      if (eventObject.connected) {
        Get.back();
      }
    });
  }

  @override
  void onPageInactive() {
    super.onPageInactive();

    Env.eventManager.unsubscribe(eventKey_networkChange);
  }
}
