import 'package:app_giftmachine/page/error/ErrorController.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_ui/page/BaseStatePage.dart';

class ErrorPage extends BaseStatePage<ErrorController> {
  const ErrorPage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: Center(
          child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cancel,
            color: Colors.red,
            size: 120.sp,
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Text(
              controller.errMsg.value,
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      )),
    );
  }
}
