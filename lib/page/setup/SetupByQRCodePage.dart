import 'package:app_giftmachine/component/DeviceStateWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/BaseStatePage.dart';
import 'package:package_ui/page/ScanCodeWidget.dart';

import 'SettingByQRCodeController.dart';

class SettingByQRCodePage extends BaseStatePage<SettingByQRCodeController> {
  const SettingByQRCodePage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: ScanCodeWidget(
        onScan: (code) async {
          await controller.scanCode(code);
        },
        child: Sized<PERSON>ox(
          height: MediaQuery.of(context).size.height,
          child: Stack(children: [
            SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: Center(
                child: Container(
                    margin: EdgeInsets.only(bottom: 0.h),
                    child: buildContent()),
              ),
            ),
            Positioned(
                bottom: 10.h,
                right: 10.w,
                child: Obx(() {
                  return DeviceStateWidget(
                    icon: Icon(
                      Icons.cast_connected_rounded,
                      size: 20.w,
                    ),
                    connected: controller.cardReaderConnected.value,
                    size: 15.w,
                  );
                })),
          ]),
        ),
      ),
    );
  }

  Widget buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 80.h,
        ),
        Text(
          '当前设备还未初始化，请按以下步骤设置'.$t,
          style: TextStyle(
            fontSize: 25.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        Lottie.asset('assets/lottie/lottie_scanning.json',
            width: 400.w, height: 400.w),
        Text(
          '请将设置码放在扫码识别区'.$t,
          style: TextStyle(
              fontSize: 20.sp, color: const Color.fromARGB(255, 170, 170, 170)),
        ),
        SizedBox(
          height: 10.h,
        ),
        GestureDetector(
          onTap: () {
            controller.scanCode(
                '{\"type\":\"setting\",\"target\":\"ticketmachine\",\"storeno\":300001,\"net\":\"ether\",\"server\":\"http://dev.erp.bingdiekeji.com/\",\"token\":\"1e55fe4b-ff00-42fc-8451-acbc014b324c_e06f12ce-f982-40a7-8710-cbc461acea69\",\"time\":\"2024-11-10 22:49:39.866048\"}');
            // Get.offNamed('/manage', arguments: {'backToLogin': true});
          },
          child: Text(
            '手动设置'.$t,
            style: TextStyle(
                color: Colors.blue,
                fontSize: 20.sp,
                decoration: TextDecoration.underline,
                decorationColor: Colors.blue),
          ),
        ),
      ],
    );
  }
}
