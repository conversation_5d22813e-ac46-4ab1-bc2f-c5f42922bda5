import 'dart:convert';

import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:get/get.dart';
import 'package:package_business/entity/MSetting.dart';
import 'package:package_business/entity/MStore.dart';
import 'package:package_business/entity/MTerminal.dart';
import 'package:package_business/services/MacService.dart';
import 'package:package_business/services/StoreTerminalService.dart';
import 'package:package_common/event/EventCardReaderConnected.dart';
import 'package:package_common/util/HttpUtil.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/BaseController.dart';

class SettingByQRCodeController extends BaseController {
  final String eventKey_cardreader_connected =
      'eventKey_setup_cardreader_connected';
  Rx<bool> cardReaderConnected = Rx(false);

  @override
  String get uniqueId => 'SettingByQRCodeController';

  @override
  void onPageActive() async {
    super.onPageActive();

    cardReaderConnected.value = Env.cardReaderManager != null;

    // 监听读卡事件
    Env.eventManager.subscribe<EventCardReaderConnected>(
        eventKey_cardreader_connected, (eventObject) async {
      cardReaderConnected.value = true;
    });
  }

  @override
  void onPageInactive() {
    Env.eventManager.unsubscribe(eventKey_cardreader_connected);

    super.onPageInactive();
  }

  Future<void> scanCode(String code) async {
    try {
      Env.promptManager.showLoading();

      // 校验设置码格式及内容
      dynamic obj = jsonDecode(code);
      MSetting setting = MSetting.fromJson(obj);
      if (setting.type == null || setting.type != 'setting') {
        throw Exception('该二维码不是设置码'.$t);
      }
      if (setting.target == null ||
          setting.target!.toLowerCase() !=
              Env.appContext.setupCodeCate.toLowerCase()) {
        throw Exception('设置的目标设备类型不正确'.$t);
      }
      if (setting.token == null || setting.token!.isEmpty) {
        throw Exception('二维码数据异常:缺少凭证'.$t);
      }
      if (setting.server == null || setting.server!.isEmpty) {
        throw Exception('二维码数据异常:缺少服务器地址'.$t);
      }

      // 检查网络是否连通，不通则按设置连接wifi
      bool networkOk = await HttpUtil.checkNetwork();
      if (!networkOk) {
        // 连接wifi
        if (setting.net == 'wifi') {
          bool isConnected =
              await Env.wifiManager.setWifi(setting.ssid!, setting.pwd!);
          if (!isConnected) {
            throw Exception('网络连接失败，请检查WIFI名称和密码是否正确'.$t);
          }
        }
      }

      // 分配设备编号
      if (Env.appContext.uniqueId.isEmpty) {
        throw Exception('设备唯一码生成失败'.$t);
      }
      int? portNO = await MacService(Env.httpManager)
          .applyPortNO(Env.appContext.uniqueId);
      if (portNO == null || portNO == 0) {
        throw Exception('自动分配机台编号失败'.$t);
      }

      Env.appContext.baseUrl = setting.server!;
      Env.appContext.portNO = portNO;
      Env.appContext.token = setting.token!;
      Env.httpManager.setBaseUrl(setting.server!);

      // 获取登录账号和密码
      var res = await StoreTerminalService(Env.httpManager).loginByQrCode(
          portNO: Env.appContext.portNO,
          cate: Env.appContext.macCate,
          token: Env.appContext.token);
      Env.appContext.loginAccount = res.account;
      Env.appContext.loginPassword = res.password;
      Env.appContext.store =
          MStore(id: res.storeId, name: res.storeName, code: res.storeNO);
      Env.appContext.terminal =
          MTerminal(id: res.terminalId, name: res.terminalName);

      // 保存配置
      await saveConfig();

      Env.promptManager.toastSuccess('设置成功'.$t);

      Get.offNamed('/');
    } catch (ex) {
      Env.promptManager.toastError(ex.toString());
    } finally {
      Env.promptManager.closeLoading();
    }
  }

  Future<void> saveConfig() async {
    await Env.cacheManager
        .set<String>(ConfigName.config_baseUrl, Env.appContext.baseUrl);
    await Env.cacheManager.set<String>(
        ConfigName.config_loginAccount, Env.appContext.loginAccount ?? '');
    await Env.cacheManager.set<String>(
        ConfigName.config_loginPassword, Env.appContext.loginPassword ?? '');
    await Env.cacheManager
        .set<int>(ConfigName.config_portNO, Env.appContext.portNO);

    if (Env.appContext.store != null) {
      String json = jsonEncode(Env.appContext.store!.toJson());
      await Env.cacheManager.set<String>(ConfigName.config_store, json);
    }
    if (Env.appContext.terminal != null) {
      String json = jsonEncode(Env.appContext.terminal!.toJson());
      await Env.cacheManager.set<String>(ConfigName.config_terminal, json);
    }
  }
}
