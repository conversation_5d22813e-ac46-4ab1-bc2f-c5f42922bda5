import 'package:app_giftmachine/page/gift/component/ExchangeCard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_business/buffer/ProdBuffer.dart';
import 'package:package_business/enum/EValueType.dart';
import 'package:package_ui/page/BaseStatePage.dart';

import 'GiftController.dart';
import 'component/MemberCard.dart';

class GiftPage extends BaseStatePage<GiftController> {
  const GiftPage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              // 会员信息详细显示区域
              SliverToBoxAdapter(
                child: Obx(() {
                  return MemberCard(member: controller.member.value);
                }),
              ),

              // 固定在顶部的兑换类型选择器
              SliverPersistentHeader(
                pinned: true,
                delegate: _ExchangeTypeSelectorDelegate(
                  child: Obx(() {
                    return ExchangeCard(
                      selectedType: controller.selectedType.value,
                      onChange: controller.changeType,
                    );
                  }),
                ),
              ),
            ];
          },
          body: Column(
            children: [
              // 搜索栏
              // Container(
              //   color: Colors.white,
              //   padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              //   child: GiftSearchBar(
              //     controller: controller.searchController,
              //     hintText: '搜索礼品名称或描述...',
              //     onChanged: controller.searchGifts,
              //     onClear: controller.clearSearch,
              //   ),
              // ),

              // 可滚动的礼品列表区域
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator(
                            strokeWidth: 4,
                          ),
                          SizedBox(height: 20.h),
                          Text(
                            '正在加载礼品...',
                            style: TextStyle(
                              fontSize: 20.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return _buildGiftGrid();
                }),
              ),
            ],
          ),
        ),
      ),
      // 右下角浮动返回按钮
      floatingActionButton: FloatingActionButton(
        onPressed: controller.goBack,
        backgroundColor: Colors.blue.shade600,
        child: Icon(
          Icons.arrow_back,
          color: Colors.white,
          size: 24.w,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  /// 构建礼品网格
  Widget _buildGiftGrid() {
    return Obx(() {
      final gifts = controller.filteredList;

      if (gifts.value.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 80.w,
                color: Colors.grey[300],
              ),
              SizedBox(height: 20.h),
              Text(
                '没有找到相关礼品',
                style: TextStyle(
                  fontSize: 20.sp,
                  color: Colors.grey[500],
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 12.h),
              Text(
                '请尝试其他搜索关键词',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        );
      }

      return Container(
        color: Colors.grey[50],
        child: GridView.builder(
          padding: EdgeInsets.all(16.w),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.75,
            crossAxisSpacing: 16.w,
            mainAxisSpacing: 16.h,
          ),
          itemCount: gifts.value.length,
          itemBuilder: (context, index) {
            final gift = gifts.value[index];
            return _buildEnhancedGiftCard(context, gift);
          },
        ),
      );
    });
  }

  /// 构建增强版礼品卡片
  Widget _buildEnhancedGiftCard(BuildContext context, ProdData gift) {
    return Obx(() {
      final selectedType = controller.selectedType.value;
      double price;
      String unit;
      Color priceColor;

      switch (selectedType) {
        case EValueType.Point:
          price = gift.pointPrice ?? 0;
          unit = '积分';
          priceColor = Colors.amber;
          break;
        case EValueType.Ticket:
          price = gift.ticketPrice ?? 0;
          unit = '彩票';
          priceColor = Colors.pink;
          break;
        default:
          price = gift.coinPrice ?? 0;
          unit = '代币';
          priceColor = Colors.orange;
          break;
      }

      final isOutOfStock = (gift.stock ?? 0) <= 0;

      return GestureDetector(
        // onTap: isOutOfStock ? null : () => controller.exchangeGift(gift),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 礼品图片
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16.r)),
                    color: Colors.grey[100],
                  ),
                  child: Stack(
                    children: [
                      // 礼品图片
                      gift.picUrl!.isNotEmpty
                          ? GestureDetector(
                              onTap: () => _showImagePreview(context, gift.picUrl!),
                              child: ClipRRect(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(16.r)),
                                child: Image.network(
                                  gift.picUrl!,
                                  width: double.infinity,
                                  height: double.infinity,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Center(
                                      child: Icon(
                                        Icons.card_giftcard,
                                        size: 60.w,
                                        color: Colors.grey[400],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            )
                          : Center(
                              child: Icon(
                                Icons.card_giftcard,
                                size: 60.w,
                                color: Colors.grey[400],
                              ),
                            ),
                      if (isOutOfStock)
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.vertical(
                                top: Radius.circular(16.r)),
                            color: Colors.black.withOpacity(0.5),
                          ),
                          child: Center(
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 6.h),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              child: Text(
                                '缺货',
                                style: TextStyle(
                                  fontSize: 20.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      //  库存标签
                      if (!isOutOfStock)
                        Positioned(
                          top: 8.h,
                          right: 8.w,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(color: Colors.green.shade300),
                            ),
                            child: Text(
                              '库存${gift.stock ?? 0}',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // 礼品信息
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 礼品名称
                      Text(
                        gift.name ?? '未知礼品',
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),

                      // Container(
                      //   padding: EdgeInsets.symmetric(
                      //       horizontal: 10.w, vertical: 2.h),
                      //   decoration: BoxDecoration(
                      //     color: Colors.green.shade100,
                      //     borderRadius: BorderRadius.circular(12.r),
                      //     border: Border.all(color: Colors.green.shade300),
                      //   ),
                      //   child: Text(
                      //     '库存 ${gift.stock ?? 0}',
                      //     style: TextStyle(
                      //       fontSize: 16.sp,
                      //       color: Colors.green.shade700,
                      //       fontWeight: FontWeight.w500,
                      //     ),
                      //   ),
                      // ),

                      const Spacer(),

                      // 价格和兑换按钮
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${price.toInt()} $unit',
                                  style: TextStyle(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.bold,
                                    color: priceColor,
                                  ),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                Text(
                                  gift.groupName ?? '其他',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // 兑换按钮
                          // Container(
                          //   padding: EdgeInsets.symmetric(
                          //       horizontal: 12.w, vertical: 6.h),
                          //   decoration: BoxDecoration(
                          //     color:
                          //         isOutOfStock ? Colors.grey[300] : priceColor,
                          //     borderRadius: BorderRadius.circular(20.r),
                          //   ),
                          //   child: Text(
                          //     isOutOfStock ? '缺货' : '兑换',
                          //     style: TextStyle(
                          //       fontSize: 12.sp,
                          //       color: Colors.white,
                          //       fontWeight: FontWeight.w600,
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
       );
     });
  }

  /// 显示图片预览
  void _showImagePreview(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        final screenSize = MediaQuery.of(context).size;
        final screenPadding = MediaQuery.of(context).padding;
        
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.8),
              child: SafeArea(
                child: Center(
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(), // 点击图片关闭预览
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: screenSize.width - 40.w,
                        maxHeight: screenSize.height - screenPadding.top - screenPadding.bottom - 80.h,
                      ),
                      margin: EdgeInsets.symmetric(
                        horizontal: 20.w,
                        vertical: 20.h,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.contain,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: double.infinity,
                              height: 300.h,
                              padding: EdgeInsets.all(40.w),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 60.w,
                                    color: Colors.grey[600],
                                  ),
                                  SizedBox(height: 16.h),
                                  Text(
                                    '图片加载失败',
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 兑换类型选择器的SliverPersistentHeaderDelegate
class _ExchangeTypeSelectorDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double? preferredHeight;

  _ExchangeTypeSelectorDelegate({required this.child, this.preferredHeight});

  @override
  double get minExtent {
    if (preferredHeight != null) {
      return preferredHeight!.clamp(0.0, double.infinity);
    }
    // 使用更保守的固定高度，确保有足够空间容纳所有内容
    return (90.h).clamp(0.0, double.infinity);
  }

  @override
  double get maxExtent {
    if (preferredHeight != null) {
      return preferredHeight!.clamp(0.0, double.infinity);
    }
    // 使用与最小高度相同的值
    return minExtent;
  }

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox(
      height: maxExtent,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _ExchangeTypeSelectorDelegate ||
        oldDelegate.preferredHeight != preferredHeight;
  }
}
