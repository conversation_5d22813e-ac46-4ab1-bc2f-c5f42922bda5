import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:package_business/enum/EValueType.dart';
import 'package:package_business/package_business.dart';
import 'package:package_ui/page/BaseController.dart';

import '../home/<USER>';
import '../../global/Env.dart';

class GiftController extends BaseController {
  // 加载状态
  final isLoading = false.obs;

  // 当前选中的兑换类型
  final Rx<EValueType?> selectedType = Rx(EValueType.Coin);

  // 过滤后的礼品列表
  final Rx<List<ProdData>> filteredList = Rx([]);

  // 当前会员信息
  final Rx<MMember> member = Rx(MMember());

  @override
  String get uniqueId => 'GiftController';

  @override
  Future<void> onPageActive() async {
    super.onPageActive();

    // 是否自动登录
    if (Get.arguments != null && Get.arguments['member'] != null) {
      member.value = Get.arguments['member'];
    }

    await getProds();
  }

  @override
  void onPageInactive() {
    super.onPageInactive();
  }

  /// 加载礼品列表
  Future<void> getProds() async {
    try {
      isLoading.value = true;

      filteredList.value = await ProdBuffer.getProdList(Env.httpManager,
          member: member.value, valueType: selectedType.value);
    } catch (e) {
      filteredList.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换兑换类型
  changeType(EValueType type) async {
    selectedType.value = type;
    await getProds();
  }

  /// 搜索礼品
  searchGifts(String query) async {
    final list = await ProdBuffer.getAll(Env.httpManager);
    if (query.isEmpty) {
      filteredList.value = list;
    } else {
      final filtered = list.where((gift) {
        final name = (gift.name ?? '').toLowerCase();
        final groupName = (gift.groupName ?? '').toLowerCase();
        final searchQuery = query.toLowerCase();

        return name.contains(searchQuery) || groupName.contains(searchQuery);
      }).toList();

      filteredList.value = filtered;
    }
  }

  /// 兑换礼品
  void exchangeGift(ProdData gift) {
    if (member.value.id == null) {
      Get.snackbar('提示', '请先登录会员账户');
      return;
    }

    double requiredAmount;
    int currentBalance;
    String unit;

    switch (selectedType.value) {
      case EValueType.Point:
        requiredAmount = gift.pointPrice;
        currentBalance = member.value.point ?? 0;
        unit = '积分';
        break;
      case EValueType.Coin:
        requiredAmount = gift.coinPrice;
        currentBalance = member.value.coin ?? 0;
        unit = '代币';
        break;
      case EValueType.Ticket:
        requiredAmount = gift.ticketPrice;
        currentBalance = member.value.ticket ?? 0;
        unit = '彩票';
        break;
      default:
        requiredAmount = 0;
        currentBalance = 0;
        unit = '未知';
        break;
    }

    if (currentBalance < requiredAmount) {
      Get.snackbar(
        '余额不足',
        '您的$unit余额不足，需要 ${requiredAmount.toInt()} $unit，当前余额 $currentBalance $unit',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    if ((gift.stock ?? 0) <= 0) {
      Get.snackbar(
        '库存不足',
        '该礼品库存不足，请选择其他礼品',
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
      );
      return;
    }

    // 执行兑换逻辑
    _performExchange(gift, requiredAmount.toInt(), unit);
  }

  /// 执行兑换
  void _performExchange(ProdData gift, int amount, String unit) {
    // 这里应该调用实际的兑换API
    // 暂时模拟兑换成功

    // 更新库存（注意：ProdData的属性可能是只读的，实际应该通过API更新）
    if (gift.stock > 0) {
      gift.stock = gift.stock - 1;
    }

    // 更新会员余额（这里应该通过HomeController来更新）
    final homeController = Get.find<HomeController>();
    // 注意：Member类的属性是final的，需要通过HomeController的方法来更新余额
    // homeController.updateMemberBalance(selectedExchangeType.value, amount);

    Get.snackbar(
      '兑换成功',
      '恭喜您成功兑换 ${gift.name ?? '未知礼品'}，消耗 $amount $unit',
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      duration: const Duration(seconds: 3),
    );

    // 跳转到成功页面
    Get.toNamed('/success');
  }

  /// 返回主页
  void goBack() {
    Get.back();
  }
}
