import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_business/enum/EValueType.dart';

class ExchangeCard extends StatelessWidget {
  EValueType? selectedType;
  final ValueChanged<EValueType>? onChange;

  ExchangeCard({super.key, this.selectedType, this.onChange});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        padding: EdgeInsets.all(6.w),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [EValueType.Coin, EValueType.Point, EValueType.Ticket]
              .map((type) {
            final isSelected = type == selectedType;
            String label = type.description;
            Color color = getColor(type);
            IconData icon = getIcon(type);

            return Expanded(
              child: GestureDetector(
                onTap: () {
                  if (onChange != null) onChange!(type);
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  margin: EdgeInsets.symmetric(horizontal: 3.w),
                  padding: EdgeInsets.symmetric(
                    vertical: 12.h,
                    horizontal: 6.w,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? color : Colors.transparent,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: color.withOpacity(0.3),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        size: 28.w,
                        color: isSelected ? Colors.white : color,
                      ),
                      SizedBox(height: 6.h),
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? Colors.white : color,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Color getColor(EValueType type) {
    switch (type) {
      case EValueType.Point:
        return Colors.amber;
      case EValueType.Ticket:
        return Colors.pink;
      default:
        return Colors.orange;
    }
  }

  IconData getIcon(EValueType type) {
    switch (type) {
      case EValueType.Point:
        return Icons.stars;
      case EValueType.Ticket:
        return Icons.confirmation_number;
      default:
        return Icons.monetization_on;
    }
  }
}
