import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import 'SuccessController.dart';

class SuccessPage extends GetView<SuccessController> {
  const SuccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: buildSuccess()),
      ),
    );
  }

  Widget buildPrinting() {
    return Padding(
      padding: EdgeInsets.only(top: 150.h),
      child: Column(
        children: [
          Lottie.asset('assets/lottie/lottie_printing.json',
              width: 400.w, height: 400.w),
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Text(
              '正在打印门票...',
              style: TextStyle(fontSize: 20.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSuccess() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 200.h),
          child: Icon(
            Icons.check_circle,
            size: 200.w,
            color: Colors.lightGreen,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: Text('打印成功', style: TextStyle(fontSize: 20.sp)),
        ),
        Padding(
            padding: EdgeInsets.only(top: 60.h),
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 300.w,
                padding: EdgeInsets.only(top: 10.h, bottom: 10.h),
                decoration: const BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.all(Radius.circular(5))),
                child: Center(
                  child: Text(
                    '确定(${controller.countDown.value})',
                    style: TextStyle(fontSize: 20.sp, color: Colors.white),
                  ),
                ),
              ),
            ))
      ],
    );
  }

  Widget buildError() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 150.h),
          child: Icon(
            Icons.error,
            size: 200.w,
            color: Colors.red,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: Text('打印门票失败，请联系管理员', style: TextStyle(fontSize: 20.sp)),
        ),
        Padding(
          padding: EdgeInsets.only(top: 20.h),
          child: Text('失败原因：', style: TextStyle(fontSize: 20.sp)),
        ),
      ],
    );
  }
}
