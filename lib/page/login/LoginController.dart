import 'dart:async';
import 'dart:convert';

import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_business/package_business.dart';
import 'package:package_common/event/EventNetworkStateChange.dart';
import 'package:package_common/util/StringUtil.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/BaseController.dart';

class LoginController extends BaseController {
  final String eventKey_networkChange = 'eventKey_login_networkChange';

  Rx<String> msg = Rx('正在加载中...'.$t);
  Timer? _loginTimer;
  bool isLogin = false;

  @override
  String get uniqueId => 'LoginController';

  @override
  onPageActive() {
    super.onPageActive();

    // 工具类语言环境
    StringUtil.setLocale(Env.appContext.langCode);

    // 订阅网络状态变更
    Env.eventManager.subscribe<EventNetworkStateChange>(eventKey_networkChange,
        (eventObject) async {
      // 如果已经处理错误页面，则不再触发
      if (!eventObject.connected) {
        stopLogin();
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          Get.toNamed('/error', arguments: {'errMsg': '网络已断开，请检查网络'.$t});
        });
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 检查是否已设置过机台
      if (Env.appContext.baseUrl.isEmpty) {
        stopLogin();
        Get.offNamed('/setup');
        return;
      }
    });

    startLogin();
  }

  @override
  onPageInactive() {
    super.onPageInactive();

    stopLogin();
  }

  /// 开始登录
  void startLogin() {
    if (_loginTimer != null) {
      _loginTimer!.cancel();
    }

    int count = 0;
    msg.value = '正在连接服务器'.$t;
    _loginTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (isLogin) return;

      await login();
      count++;
      msg.value = '${'正在连接服务器'.$t}...$count';
    });
  }

  Future<void> login() async {
    try {
      isLogin = true;

      if (Env.appContext.baseUrl.isEmpty) {
        throw Exception('未设置服务器地址'.$t);
      }
      if (Env.appContext.store == null) {
        throw Exception('未设置门店'.$t);
      }
      if (Env.appContext.terminal == null) {
        throw Exception('未设置终端'.$t);
      }

      await applyPortNO();

      // 登录
      var token = await StoreTerminalService(Env.httpManager).loginByDevice(
          Env.appContext.store!.id!,
          Env.appContext.terminal!.id!,
          Env.appContext.loginAccount!,
          Env.appContext.loginPassword!);
      Env.appContext.token = token;
      Env.httpManager.setToken(Env.appContext.token);

      // 获取并保存端口信息
      Env.appContext.macPort =
          await MacPortService(Env.httpManager).get(Env.appContext.portNO);
      if (Env.appContext.macPort != null) {
        String json = jsonEncode(Env.appContext.macPort!.toJson());
        await Env.cacheManager.set<String>(ConfigName.config_macPort, json);
      }

      stopLogin();

      Get.offAllNamed('/giftquery');
    } catch (e) {
      Env.promptManager.toastError(e.toString());
    } finally {
      isLogin = false;
    }
  }

  applyPortNO() async {
    if (Env.appContext.portNO > 0) {
      return;
    }

    // 分配设备编号
    if (Env.appContext.uniqueId.isEmpty) {
      throw Exception('设备唯一码生成失败'.$t);
    }
    int? portNO =
        await MacService(Env.httpManager).applyPortNO(Env.appContext.uniqueId);
    if (portNO == null || portNO == 0) {
      throw Exception('自动分配机台编号失败'.$t);
    }
    Env.appContext.portNO = portNO;
    await Env.cacheManager
        .set<int>(ConfigName.config_portNO, Env.appContext.portNO);
  }

  stopLogin() {
    Env.eventManager.unsubscribe(eventKey_networkChange);

    if (_loginTimer != null) {
      _loginTimer!.cancel();
    }
  }
}
