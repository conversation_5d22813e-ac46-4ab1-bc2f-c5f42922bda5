import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/BaseStatePage.dart';

import 'LoginController.dart';

class LoginPage extends BaseStatePage<LoginController> {
  const LoginPage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Obx(() {
          return GestureDetector(
              onTap: () {
                controller.stopLogin();
                Get.offNamed('/manage', arguments: {'backToLogin': true});
              },
              child: buildContent());
        }),
      ),
    );
  }

  Widget buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Lottie.asset('assets/lottie/lottie_loading.json',
            width: 400.w, height: 400.w),
        Text(
          controller.msg.value,
          style: TextStyle(fontSize: 20.sp),
        )
      ],
    );
  }

  Widget buildNetworkError() {
    return Center(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.cancel,
          color: Colors.red,
          size: 160.sp,
        ),
        Padding(
          padding: EdgeInsets.only(top: 10.h),
          child: Text(
            '网络断开，请检查网络'.$t,
            style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(
          height: 10.h,
        ),
        Text('正在重试...'.$t)
      ],
    ));
  }
}
