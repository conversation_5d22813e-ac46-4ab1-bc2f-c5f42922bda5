import 'package:app_giftmachine/global/Env.dart';
import 'package:app_giftmachine/page/manage/communication/CommunicationWidget.dart';
import 'package:app_giftmachine/page/manage/other/OtherWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/button/BlockButton.dart';
import 'package:restart_app/restart_app.dart';

import 'ManageController.dart';

class ManagePage extends GetView<ManageController> {
  const ManagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (controller.backToLogin.value) {
              Get.offAllNamed('/login');
              return;
            }
            Get.back();
          },
        ),
        title: Text('设备管理'.$t),
      ),
      body: Container(
        margin: const EdgeInsets.only(left: 10, right: 10),
        child: ListView(
          shrinkWrap: true,
          children: [
            buildBlock(context,
                title: '通信设置'.$t,
                child: CommunicationWidget(),
                icon: const Icon(Icons.connected_tv)),
            buildBlock(context,
                title: '其他设置'.$t,
                child: const OtherWidget(),
                icon: const Icon(Icons.build)),
            buildBottom(context)
          ],
        ),
      ),
    );
  }

  Widget buildBlock(BuildContext context,
      {required String title, required Widget child, Icon? icon}) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 20.h, left: 5.w, right: 5.w, bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                icon ??
                    const Icon(
                      Icons.settings,
                    ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  title,
                  style: TextStyle(fontSize: 20.sp),
                ),
              ],
            ),
          ),
          const Divider(
            thickness: 0.5,
            color: Colors.black,
          ),
          child
        ],
      ),
    );
  }

  Widget buildBottom(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 20.h, left: 5.w, right: 5.w, bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          BlockButton(
            title: '返回'.$t,
            height: 50.h,
            style: TextStyle(color: Colors.white, fontSize: 20.sp),
            onTap: () {
              if (controller.backToLogin.value) {
                Get.offAllNamed('/login');
                return;
              }
              Get.back();
            },
          ),
          SizedBox(
            height: 20.h,
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: BlockButton(
                  title: '退出软件'.$t,
                  height: 50.h,
                  bgColor: Colors.red,
                  style: TextStyle(color: Colors.white, fontSize: 20.sp),
                  onTap: () {
                    SystemNavigator.pop();
                  },
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                flex: 1,
                child: BlockButton(
                  title: '重启软件'.$t,
                  height: 50.h,
                  bgColor: Colors.red,
                  style: TextStyle(color: Colors.white, fontSize: 20.sp),
                  onTap: () async {
                    await Env.dispose();

                    Restart.restartApp();
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
