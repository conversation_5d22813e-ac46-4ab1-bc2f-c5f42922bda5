import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/form/FormBase.dart';
import 'package:package_ui/form/FormInput.dart';
import 'package:package_ui/form/FormSelector.dart';

import 'CommunicationController.dart';

class CommunicationWidget extends GetView<CommunicationController> {
  final GlobalKey<FormInputState> _gpioNameKey = GlobalKey<FormInputState>();

  CommunicationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return buildContent();
    });
  }

  Widget buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 5.h,
        ),
        FormInput(
          title: '服务器地址'.$t,
          defaultValue: Env.appContext.baseUrl ?? '',
          titleStyle: TextStyle(fontSize: 20.sp),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          onChanged: (val) {
            controller.serverUrlChanged(val);
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        FormSelector(
          title: '绑定门店'.$t,
          value: controller.storeName.value,
          titleStyle: TextStyle(fontSize: 20.sp),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          onTap: () async {
            await controller.showStoreSelection();
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        FormInput(
          title: '登录账号'.$t,
          defaultValue: controller.loginAccount.value,
          hintText: '请输入'.$t,
          titleStyle: TextStyle(fontSize: 20.sp),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          onChanged: (val) async {
            await controller.loginAccountChanged(val);
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        FormInput(
          title: '登录密码'.$t,
          defaultValue: controller.loginPassword.value,
          hintText: '请输入'.$t,
          titleStyle: TextStyle(fontSize: 20.sp),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          obscureText: true,
          onChanged: (val) async {
            await controller.loginPasswordChanged(val);
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        FormSelector(
          title: '绑定终端'.$t,
          value: controller.terminalName.value,
          titleStyle: TextStyle(fontSize: 20.sp),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          onTap: () async {
            await controller.showTerminalSelection();
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        FormBase(
          title: Text('端口号'.$t, style: TextStyle(fontSize: 20.sp)),
          content: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Visibility(
                visible: Env.appContext.portNO == 0,
                child: Text(
                  '登录时将自动获取'.$t,
                  style:
                      TextStyle(color: Colors.grey.shade500, fontSize: 20.sp),
                ),
              ),
              Visibility(
                visible: Env.appContext.portNO > 0,
                child: Text(
                  'NO.${Env.appContext.portNO}',
                  style:
                      TextStyle(color: Colors.grey.shade500, fontSize: 20.sp),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
