import 'dart:async';
import 'dart:convert';

import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_business/package_business.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/SelectorWidget.dart';

class CommunicationController extends GetxController {
  Rx<String> serverUrl = Rx('');
  Rx<String> storeName = Rx('');
  Rx<String> terminalName = Rx('');
  Rx<String> loginAccount = Rx('');
  Rx<String> loginPassword = Rx('');
  Rx<int?> portNO = Rx(null);
  Rx<bool> needApplyPortNO = true.obs;

  List<Map<String, dynamic>> _stores = [];
  List<Map<String, dynamic>> _terminals = [];
  bool _isLoadingData = false;
  Timer? _inputTimer;

  @override
  Future<void> onInit() async {
    super.onInit();

    await initConfig();
  }

  initConfig() async {
    serverUrl.value = Env.appContext.baseUrl;
    if (Env.appContext.store != null && Env.appContext.store!.name != null) {
      storeName.value = Env.appContext.store!.name ?? '--';
    }
    if (Env.appContext.terminal != null &&
        Env.appContext.terminal!.name != null) {
      terminalName.value = Env.appContext.terminal!.name ?? '--';
    }

    loginAccount.value = Env.appContext.loginAccount ?? '--';
    loginPassword.value = Env.appContext.loginPassword ?? '--';
    portNO.value = Env.appContext.portNO;
  }

  loadSelections() async {
    if (Env.appContext.baseUrl.isNotEmpty) {
      if (_stores.isEmpty) {
        await getStores();
      }
    }
    if (Env.appContext.store != null) {
      if (_terminals.isEmpty) {
        await getTerminals();
      }
    }
  }

  getStores() async {
    try {
      _isLoadingData = true;

      if (!Env.appContext.baseUrl.startsWith('http://') &&
          !Env.appContext.baseUrl.startsWith('https://')) {
        return;
      }
      if (Env.appContext.baseUrl.length < 11) {
        return;
      }

      final caches = await StoreService(Env.httpManager).getList();
      for (var cache in caches) {
        _stores.add(cache.toJson());
      }
    } finally {
      _isLoadingData = false;
    }
  }

  getTerminals() async {
    try {
      _isLoadingData = true;

      if (Env.appContext.baseUrl.length < 10) {
        return;
      }
      if (Env.appContext.store == null) {
        return;
      }

      final caches = await StoreTerminalService(Env.httpManager)
          .getList(Env.appContext.store!.id!);
      for (var cache in caches) {
        _terminals.add(cache.toJson());
      }
    } finally {
      _isLoadingData = false;
    }
  }

  serverUrlChanged(String val) {
    if (_inputTimer?.isActive ?? false) _inputTimer!.cancel();
    _inputTimer = Timer(const Duration(milliseconds: 100), () async {
      serverUrl.value = val;
      await Env.cacheManager
          .set(ConfigName.config_baseUrl, serverUrl.value ?? '');
      Env.appContext.baseUrl = serverUrl.value;
      Env.httpManager.setBaseUrl(Env.appContext.baseUrl);
    });
  }

  storeChanged(MStore store) async {
    storeName.value = store.name ?? '--';
    String json = jsonEncode(store.toJson());
    await Env.cacheManager.set(ConfigName.config_store, json);
    Env.appContext.store = store;

    await loadSelections();
  }

  loginAccountChanged(String val) async {
    loginAccount.value = val;
    await Env.cacheManager
        .set(ConfigName.config_loginAccount, loginAccount.value);
    Env.appContext.loginAccount = loginAccount.value;
  }

  loginPasswordChanged(String val) async {
    loginPassword.value = val;
    await Env.cacheManager
        .set(ConfigName.config_loginPassword, loginPassword.value ?? '');
    Env.appContext.loginPassword = loginPassword.value;
  }

  portNOChanged() async {
    await Env.cacheManager
        .set<int>(ConfigName.config_portNO, portNO.value ?? 0);
    Env.appContext.portNO = portNO.value ?? 0;
  }

  terminalChanged(MTerminal terminal) async {
    terminalName.value = terminal.name ?? '--';
    String json = jsonEncode(terminal.toJson());
    await Env.cacheManager.set(ConfigName.config_terminal, json);
    Env.appContext.terminal = terminal;
  }

  applyMacNO() async {
    try {
      Env.promptManager.showLoading();

      if (Env.appContext.uniqueId.isEmpty) {
        throw Exception('设备唯一码生成失败'.$t);
      }
      int? applyPortNO = await MacService(Env.httpManager)
          .applyPortNO(Env.appContext.uniqueId);
      if (applyPortNO == null || applyPortNO == 0) {
        throw Exception('自动分配机台编号失败'.$t);
      }

      Env.appContext.portNO = applyPortNO;
      portNO.value = applyPortNO;
      needApplyPortNO.value = false;
      await Env.cacheManager
          .set<int>(ConfigName.config_portNO, portNO.value ?? 0);
    } catch (e) {
      Env.promptManager.toastError(e.toString());
    } finally {
      Env.promptManager.closeLoading();
    }
  }

  showStoreSelection() async {
    if (_isLoadingData) return;

    if (_stores.isEmpty) {
      try {
        Env.promptManager.showLoading();
        await getStores();
      } catch (e) {
        Env.promptManager.toastError('获取失败，请检查服务器地址'.$t);
      } finally {
        Env.promptManager.closeLoading();
      }
    }

    Env.promptManager.showCustomDialog(
      width: 400,
      SelectorWidget(
        title: '请选择门店'.$t,
        displayPropName: 'Name',
        uniquePropName: 'Id',
        defaultValue:
            Env.appContext.store == null || Env.appContext.store!.id == null
                ? null
                : Env.appContext.store!.id,
        items: _stores,
        prefixWidget: const Padding(
          padding: EdgeInsets.only(right: 10),
          child: Icon(
            Icons.store,
            color: Colors.blue,
          ),
        ),
        onItemClick: (data) async {
          final store = MStore.fromJson(data);
          await storeChanged(store);
          Get.back();
        },
      ),
    );
  }

  showTerminalSelection() async {
    if (_isLoadingData) return;

    if (_terminals.isEmpty) {
      try {
        Env.promptManager.showLoading();
        await getTerminals();
      } catch (e) {
        Env.promptManager.toastError('获取失败，请检查服务器地址'.$t);
      } finally {
        Env.promptManager.closeLoading();
      }
    }

    Env.promptManager.showCustomDialog(
      width: 400,
      SelectorWidget(
        title: '请选择终端'.$t,
        displayPropName: 'Name',
        uniquePropName: 'Id',
        defaultValue: Env.appContext.terminal == null ||
                Env.appContext.terminal!.id == null
            ? null
            : Env.appContext.terminal!.id,
        items: _terminals,
        prefixWidget: const Padding(
          padding: EdgeInsets.only(right: 10),
          child: Icon(
            Icons.devices,
            color: Colors.blue,
          ),
        ),
        onItemClick: (data) async {
          final terminal = MTerminal.fromJson(data);
          await terminalChanged(terminal);
          Get.back();
        },
      ),
    );
  }
}
