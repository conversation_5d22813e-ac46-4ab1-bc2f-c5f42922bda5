import 'dart:convert';

import 'package:app_giftmachine/global/ConfigName.dart';
import 'package:app_giftmachine/global/Env.dart';
import 'package:app_giftmachine/global/ThemeContext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_common/util/StringUtil.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/page/RestartWidget.dart';
import 'package:package_ui/page/SelectorWidget.dart';
import 'package:package_ui/page/UpgradeWidget.dart';

class OtherController extends GetxController {
  Rx<String> lang = Rx('');
  List<Map<String, dynamic>> languages = [];
  Rx<List<int>> dpi = Rx([600, 800]);

  @override
  Future<void> onInit() async {
    super.onInit();

    initLanguages();
    initDpi();
  }

  initLanguages() {
    languages.add({'code': 'zh', 'name': '中文(简体)'});
    languages.add({'code': 'en', 'name': 'English'});
    languages.add({'code': 'ar', 'name': 'العربية'});
    lang.value = languages[0]['name'];

    lang.value = languages
        .firstWhere((l) => l['code'] == Env.appContext.langCode)['name'];
  }

  initDpi() {
    if (Env.appContext.dpi != null) {
      dpi.value = Env.appContext.dpi!;
    }
  }

  langChanged(Map<String, dynamic> val) async {
    lang.value = val['name'];
    String langCode = val['code'];
    await Env.cacheManager.set(ConfigName.config_language, langCode);
    Env.appContext.langCode = langCode;
    Env.i18nManager.change(Get.context!, langCode);
    StringUtil.setLocale(langCode);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Get.forceAppUpdate();
    });
  }

  void showLanguageSelection() {
    Env.promptManager.showCustomDialog(
      width: 400,
      SelectorWidget(
        title: '请选择语言'.$t,
        displayPropName: 'name',
        uniquePropName: 'code',
        defaultValue: Env.appContext.langCode,
        items: languages,
        prefixWidget: const Padding(
          padding: EdgeInsets.only(right: 10),
          child: Icon(
            Icons.language,
            color: Colors.blue,
          ),
        ),
        onItemClick: (data) async {
          await langChanged(data);
          Get.back();

          final res = await Env.promptManager.confirm('设置成功重启后生效，是否立即重启？'.$t);
          if (res) {
            Future.delayed(const Duration(milliseconds: 500), () {
              RestartWidget.restartApp(Get.context!);
            });
          }
        },
      ),
    );
  }

  checkUpgrade() async {
    try {
      Env.promptManager.showLoading();

      final upgradeContext = await Env.upgradeManager
          .checkUpgrade(offlineVersionCode: Env.appContext.offlineVersionCode);
      if (!(upgradeContext.needUpgrade ?? false)) {
        Env.promptManager.toastInfo('当前已是最新版本'.$t);
        return;
      }

      Env.promptManager.showCustomDialog(
          width: 500,
          UpgradeWidget(
            upgradeManager: Env.upgradeManager,
            upgradeContext: upgradeContext,
            packageName: Env.appContext.packageName,
            titleSize: 20.sp,
            versionSize: 16.sp,
            contentSize: 16.sp,
            upgradeTitleSize: 16.sp,
            upgradeButtonHeight: 30.h,
            notNowTitleSize: 16.sp,
          ));
    } catch (e) {
      Env.promptManager.toastError(e.toString());
    } finally {
      Env.promptManager.closeLoading();
    }
  }

  dpi1Change(int val) async {
    dpi.value[0] = val;

    await Env.cacheManager.set(ConfigName.config_dpi, jsonEncode(dpi.value));
  }

  dpi2Change(int val) async {
    dpi.value[1] = val;
    await Env.cacheManager.set(ConfigName.config_dpi, jsonEncode(dpi.value));
  }

  dpiReset() async {
    dpi.value = [600, 800];
    await Env.cacheManager.set(ConfigName.config_dpi, '');
  }
}
