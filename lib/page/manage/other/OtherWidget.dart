import 'package:app_giftmachine/global/Env.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_i18n/TranslateExtension.dart';
import 'package:package_ui/form/FormBase.dart';
import 'package:package_ui/form/FormSelector.dart';
import 'package:package_ui/other/InputNumber.dart';

import 'OtherController.dart';

class OtherWidget extends GetView<OtherController> {
  const OtherWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return buildContent(context);
    });
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 5.h,
        ),
        FormSelector(
          title: '语言'.$t,
          subTitle: Text('重启后生效'.$t,
              style: TextStyle(fontSize: 15.sp, color: Colors.grey.shade400)),
          value: controller.lang.value,
          titleStyle: TextStyle(fontSize: 20.sp),
          hintStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          valueStyle: TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
          onTap: () {
            controller.showLanguageSelection();
          },
        ),
        SizedBox(
          height: 5.h,
        ),
        buildDpiSetting(),
        SizedBox(
          height: 5.h,
        ),
        FormBase(
          title: Text('调试窗口'.$t, style: TextStyle(fontSize: 20.sp)),
          content: GestureDetector(
            onTap: () async {
              await controller.checkUpgrade();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () {
                    Env.debugManager
                        .createWindow(context, slideBottomHeight: -300);
                  },
                  child: Text(
                    '开启'.$t,
                    style: TextStyle(color: Colors.blue, fontSize: 20.sp),
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 5.h,
        ),
        FormBase(
          title: Text('检查更新'.$t, style: TextStyle(fontSize: 20.sp)),
          content: GestureDetector(
            onTap: () async {
              await controller.checkUpgrade();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Container(
                //   width: 10,
                //   height: 10,
                //   margin: EdgeInsets.only(right: 10.w),
                //   decoration: BoxDecoration(
                //       color: Colors.red,
                //       borderRadius: BorderRadius.all(Radius.circular(20.w))),
                // ),
                Text(
                  Env.appContext.version,
                  style:
                      TextStyle(fontSize: 20.sp, color: Colors.grey.shade500),
                ),
                SizedBox(
                  width: 15.w,
                ),
                Icon(Icons.arrow_forward_ios_outlined,
                    size: 20.sp, color: Colors.grey.shade500),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget buildDpiSetting() {
    return FormBase(
      title: Text('分辨率'.$t, style: TextStyle(fontSize: 20.sp)),
      subTitle: Text('重启后生效'.$t,
          style: TextStyle(fontSize: 10.sp, color: Colors.grey.shade400)),
      leftFlex: 2,
      rightFlex: 8,
      content: GestureDetector(
        onTap: () async {
          await controller.checkUpgrade();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            InputNumber(
              minValue: 0,
              maxValue: 2560,
              style: TextStyle(fontSize: 15.sp),
              defaultValue: controller.dpi.value[0],
              buttonVisible: false,
              height: 20.h,
              onChange: (val) async {
                await controller.dpi1Change(val);
              },
            ),
            SizedBox(
              width: 5.w,
            ),
            Text(
              'x',
              style: TextStyle(color: Colors.grey.shade300),
            ),
            SizedBox(
              width: 5.w,
            ),
            InputNumber(
              minValue: 0,
              maxValue: 2560,
              style: TextStyle(fontSize: 15.sp),
              defaultValue: controller.dpi.value[1],
              buttonVisible: false,
              height: 20.h,
              onChange: (val) async {
                await controller.dpi2Change(val);
              },
            ),
            SizedBox(
              width: 5.w,
            ),
            GestureDetector(
              onTap: () async {
                await controller.dpiReset();
              },
              child: Text(
                '重置'.$t,
                style: TextStyle(fontSize: 20.sp, color: Colors.blue),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
