import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_business/package_business.dart';
import '../GiftQueryController.dart';

class GiftQueryCard extends StatelessWidget {
  final ProdData gift;
  final GiftStatus status;
  final Map<String, dynamic> priceInfo;
  final VoidCallback? onImageTap;
  final VoidCallback? onTap;

  const GiftQueryCard({
    super.key,
    required this.gift,
    required this.status,
    required this.priceInfo,
    this.onImageTap,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey[50]!,
            ],
          ),
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: _buildImageSection(),
              ),
              Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(child: _buildTitle()),
                      SizedBox(height: 4.h),
                      _buildPriceSection(),
                      SizedBox(height: 4.h),
                      _buildStatusSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return GestureDetector(
      onTap: onImageTap,
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[100]!,
              Colors.grey[200]!,
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Stack(
            children: [
              gift.picUrl?.isNotEmpty == true
                  ? Image.network(
                      gift.picUrl!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholder();
                      },
                    )
                  : _buildPlaceholder(),
              // 添加渐变遮罩
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 30.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[100],
      child: Icon(
        Icons.card_giftcard,
        size: 40.w,
        color: Colors.grey[400],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      gift.name ?? '未知礼品',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w700,
        color: Colors.black87,
        height: 1.2,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            (priceInfo['color'] ?? Colors.orange).withOpacity(0.1),
            (priceInfo['color'] ?? Colors.orange).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${priceInfo['price']?.toInt() ?? 0}',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: priceInfo['color'] ?? Colors.orange,
            ),
          ),
          SizedBox(width: 4.w),
          Text(
            priceInfo['unit'] ?? '',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection() {
    Widget statusWidget;
    
    switch (status) {
      case GiftStatus.outOfStock:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.red[100]!, Colors.red[50]!],
            ),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.red[200]!, width: 1),
          ),
          child: Text(
            '缺货',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.red[700],
              fontWeight: FontWeight.w600,
            ),
          ),
        );
        break;
      case GiftStatus.insufficientBalance:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[100]!, Colors.orange[50]!],
            ),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.orange[200]!, width: 1),
          ),
          child: Text(
            '余额不足',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.orange[700],
              fontWeight: FontWeight.w600,
            ),
          ),
        );
        break;
      case GiftStatus.available:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green[100]!, Colors.green[50]!],
            ),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.green[200]!, width: 1),
          ),
          child: Text(
            '可兑换',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.green[700],
              fontWeight: FontWeight.w600,
            ),
          ),
        );
        break;
    }

    return Row(
      children: [
        statusWidget,
        const Spacer(),
        if (gift.stock != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Text(
              '库存: ${gift.stock}',
              style: TextStyle(
                fontSize: 11.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}