import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_business/package_business.dart';
import '../GiftQueryController.dart';

class GiftQueryCard extends StatelessWidget {
  final ProdData gift;
  final GiftStatus status;
  final Map<String, dynamic> priceInfo;
  final VoidCallback? onImageTap;
  final VoidCallback? onTap;

  const GiftQueryCard({
    super.key,
    required this.gift,
    required this.status,
    required this.priceInfo,
    this.onImageTap,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTitle(),
                    const Spacer(),
                    _buildPriceSection(),
                    SizedBox(height: 4.h),
                    _buildStatusSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Expanded(
      flex: 3,
      child: GestureDetector(
        onTap: onImageTap,
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            color: Colors.grey[100],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            child: gift.picUrl?.isNotEmpty == true
                ? Image.network(
                    gift.picUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildPlaceholder();
                    },
                  )
                : _buildPlaceholder(),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[100],
      child: Icon(
        Icons.card_giftcard,
        size: 40.w,
        color: Colors.grey[400],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      gift.name ?? '未知礼品',
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceSection() {
    return Row(
      children: [
        Text(
          '${priceInfo['price']?.toInt() ?? 0}',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: priceInfo['color'] ?? Colors.orange,
          ),
        ),
        SizedBox(width: 2.w),
        Text(
          priceInfo['unit'] ?? '',
          style: TextStyle(
            fontSize: 10.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    Widget statusWidget;
    
    switch (status) {
      case GiftStatus.outOfStock:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.red[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '缺货',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.red[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
      case GiftStatus.insufficientBalance:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.orange[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '余额不足',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.orange[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
      case GiftStatus.available:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.green[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '可兑换',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.green[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
    }

    return Row(
      children: [
        statusWidget,
        const Spacer(),
        if (gift.stock != null)
          Text(
            '库存: ${gift.stock}',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.grey[500],
            ),
          ),
      ],
    );
  }
}