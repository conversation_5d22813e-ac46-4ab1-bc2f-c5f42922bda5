import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_business/package_business.dart';
import '../GiftQueryController.dart';

class GiftQueryCard extends StatelessWidget {
  final ProdData gift;
  final GiftStatus status;
  final Map<String, dynamic> priceInfo;
  final VoidCallback? onImageTap;
  final VoidCallback? onTap;

  const GiftQueryCard({
    super.key,
    required this.gift,
    required this.status,
    required this.priceInfo,
    this.onImageTap,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey[50]!,
            ],
          ),
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.8),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImageSection(),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTitle(),
                      SizedBox(height: 8.h),
                      _buildPriceSection(),
                      const Spacer(),
                      _buildStatusSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Expanded(
      flex: 3,
      child: GestureDetector(
        onTap: onImageTap,
        child: Container(
          width: double.infinity,
          margin: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey[100]!,
                Colors.grey[200]!,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Stack(
              children: [
                gift.picUrl?.isNotEmpty == true
                    ? Image.network(
                        gift.picUrl!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholder();
                        },
                      )
                    : _buildPlaceholder(),
                // 添加渐变遮罩
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 30.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.1),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[100],
      child: Icon(
        Icons.card_giftcard,
        size: 40.w,
        color: Colors.grey[400],
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      gift.name ?? '未知礼品',
      style: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceSection() {
    return Row(
      children: [
        Text(
          '${priceInfo['price']?.toInt() ?? 0}',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: priceInfo['color'] ?? Colors.orange,
          ),
        ),
        SizedBox(width: 2.w),
        Text(
          priceInfo['unit'] ?? '',
          style: TextStyle(
            fontSize: 10.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    Widget statusWidget;
    
    switch (status) {
      case GiftStatus.outOfStock:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.red[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '缺货',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.red[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
      case GiftStatus.insufficientBalance:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.orange[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '余额不足',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.orange[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
      case GiftStatus.available:
        statusWidget = Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: Colors.green[100],
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            '可兑换',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.green[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        );
        break;
    }

    return Row(
      children: [
        statusWidget,
        const Spacer(),
        if (gift.stock != null)
          Text(
            '库存: ${gift.stock}',
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.grey[500],
            ),
          ),
      ],
    );
  }
}