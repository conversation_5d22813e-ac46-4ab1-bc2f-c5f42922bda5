import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_business/buffer/ProdBuffer.dart';
import 'package:package_business/enum/EValueType.dart';
import 'package:package_business/package_business.dart';
import 'package:package_ui/page/BaseController.dart';
import '../../global/Env.dart';

class GiftQueryController extends BaseController {
  final isLoading = false.obs;
  final Rx<EValueType?> selectedType = Rx(EValueType.Coin);
  final Rx<List<ProdData>> filteredList = Rx([]);
  final Rx<MMember> member = Rx(MMember());
  final showAd = false.obs;
  Timer? _inactivityTimer;
  
  @override
  String get uniqueId => 'GiftQueryController';

  @override
  Future<void> onPageActive() async {
    super.onPageActive();
    if (Get.arguments != null && Get.arguments['member'] != null) {
      member.value = Get.arguments['member'];
    }
    await loadGifts();
    _startInactivityTimer();
  }

  @override
  void onPageInactive() {
    _cancelInactivityTimer();
    super.onPageInactive();
  }

  Future<void> loadGifts() async {
    try {
      isLoading.value = true;
      filteredList.value = await ProdBuffer.getProdList(
        Env.httpManager,
        member: member.value,
        valueType: selectedType.value,
      );
    } catch (e) {
      filteredList.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> changeType(EValueType type) async {
    selectedType.value = type;
    await loadGifts();
    _resetInactivityTimer();
  }

  GiftStatus getGiftStatus(ProdData gift) {
    if ((gift.stock ?? 0) <= 0) {
      return GiftStatus.outOfStock;
    }
    
    double requiredAmount;
    int currentBalance;
    
    switch (selectedType.value) {
      case EValueType.Point:
        requiredAmount = gift.pointPrice ?? 0;
        currentBalance = member.value.point ?? 0;
        break;
      case EValueType.Coin:
        requiredAmount = gift.coinPrice ?? 0;
        currentBalance = member.value.coin ?? 0;
        break;
      case EValueType.Ticket:
        requiredAmount = gift.ticketPrice ?? 0;
        currentBalance = member.value.ticket ?? 0;
        break;
      default:
        requiredAmount = 0;
        currentBalance = 0;
        break;
    }
    
    if (currentBalance < requiredAmount) {
      return GiftStatus.insufficientBalance;
    }
    
    return GiftStatus.available;
  }

  Map<String, dynamic> getGiftPriceInfo(ProdData gift) {
    switch (selectedType.value) {
      case EValueType.Point:
        return {'price': gift.pointPrice ?? 0, 'unit': '积分', 'color': Colors.amber};
      case EValueType.Ticket:
        return {'price': gift.ticketPrice ?? 0, 'unit': '彩票', 'color': Colors.pink};
      default:
        return {'price': gift.coinPrice ?? 0, 'unit': '代币', 'color': Colors.orange};
    }
  }

  void showImagePreview(String imageUrl) {
    _resetInactivityTimer();
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: GestureDetector(
          onTap: () => Get.back(),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: InteractiveViewer(
              child: Image.network(
                imageUrl,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Icon(Icons.error, size: 100, color: Colors.white),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void onUserInteraction() => _resetInactivityTimer();
  
  void showFullScreenAd() => showAd.value = true;
  
  void hideFullScreenAd() {
    showAd.value = false;
    _startInactivityTimer();
  }

  void _startInactivityTimer() {
    _cancelInactivityTimer();
    _inactivityTimer = Timer(const Duration(seconds: 30), showFullScreenAd);
  }

  void _resetInactivityTimer() => _startInactivityTimer();
  
  void _cancelInactivityTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = null;
  }

  @override
  void onClose() {
    _cancelInactivityTimer();
    super.onClose();
  }
}

enum GiftStatus { 
  available, 
  outOfStock, 
  insufficientBalance 
}