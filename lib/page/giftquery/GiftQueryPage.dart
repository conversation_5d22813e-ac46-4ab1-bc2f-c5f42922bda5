import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_ui/page/BaseStatePage.dart';
import 'package:package_business/package_business.dart';

import 'GiftQueryController.dart';
import 'component/TypeSelector.dart';
import 'component/GiftQueryCard.dart';
import 'component/FullScreenAd.dart';

class GiftQueryPage extends BaseStatePage<GiftQueryController> {
  const GiftQueryPage({super.key});

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: GestureDetector(
          onTap: controller.onUserInteraction,
          onPanDown: (_) => controller.onUserInteraction(),
          child: Stack(
            children: [
              Column(
                children: [
                  Container(
                    color: Colors.white,
                    child: Obx(() => TypeSelector(
                      selectedType: controller.selectedType.value,
                      onTypeChanged: controller.changeType,
                    )),
                  ),
                  Expanded(
                    child: Obx(() {
                      if (controller.isLoading.value) {
                        return _buildLoadingWidget();
                      }
                      return _buildGiftGrid();
                    }),
                  ),
                ],
              ),
              Obx(() {
                if (controller.showAd.value) {
                  return FullScreenAd(onTap: controller.hideFullScreenAd);
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(strokeWidth: 4),
          SizedBox(height: 20.h),
          Text(
            '正在加载礼品...',
            style: TextStyle(fontSize: 20.sp, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildGiftGrid() {
    final gifts = controller.filteredList.value;

    if (gifts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.card_giftcard_outlined, size: 80.w, color: Colors.grey[300]),
            SizedBox(height: 20.h),
            Text(
              '暂无礼品',
              style: TextStyle(fontSize: 20.sp, color: Colors.grey[500], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return Container(
      color: Colors.grey[50],
      child: GridView.builder(
        padding: EdgeInsets.all(16.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
        ),
        itemCount: gifts.length,
        itemBuilder: (context, index) {
          final gift = gifts[index];
          return GiftQueryCard(
            gift: gift,
            status: controller.getGiftStatus(gift),
            priceInfo: controller.getGiftPriceInfo(gift),
            onImageTap: () {
              if (gift.picUrl?.isNotEmpty == true) {
                controller.showImagePreview(gift.picUrl!);
              }
            },
            onTap: controller.onUserInteraction,
          );
        },
      ),
    );
  }
}